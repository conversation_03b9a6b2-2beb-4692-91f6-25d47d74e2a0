<?php
/**
 * View file for Positions with Applications Report
 *
 * @var array $exercise Exercise details
 * @var array $positions List of positions with applications
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>">Application Register</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Positions with Applications
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Positions with Applications</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Application Register
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border rounded p-3">
                                <i class="fas fa-briefcase fa-2x text-info mb-2"></i>
                                <h4 class="text-info mb-1"><?= count($positions) ?></h4>
                                <p class="mb-0 text-muted">Positions with Applications</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3">
                                <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                                <h4 class="text-success mb-1">
                                    <?= array_sum(array_column($positions, 'application_count')) ?>
                                </h4>
                                <p class="mb-0 text-muted">Total Applications</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3">
                                <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary mb-1">
                                    <?= count($positions) > 0 ? round(array_sum(array_column($positions, 'application_count')) / count($positions), 1) : 0 ?>
                                </h4>
                                <p class="mb-0 text-muted">Average Applications per Position</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Positions with Applications (<?= count($positions) ?> Positions)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No positions with applications found</h5>
                            <p class="text-muted">All positions in this exercise currently have no applications.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="positionsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="25%">Position Details</th>
                                        <th width="15%">Position Group</th>
                                        <th width="15%">Organization</th>
                                        <th width="15%">Classification & Award</th>
                                        <th width="10%">Applications</th>
                                        <th width="15%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($position['designation']) ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        Ref: <?= esc($position['position_reference']) ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-info">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?= esc($position['location']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <?= esc($position['group_name']) ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= esc($position['org_name']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="d-block">
                                                        <strong>Class:</strong> <?= esc($position['classification']) ?>
                                                    </small>
                                                    <small class="d-block">
                                                        <strong>Award:</strong> <?= esc($position['award']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success fs-6">
                                                    <?= $position['application_count'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical" role="group">
                                                    <a href="<?= base_url('reports/application-register-position/' . $position['id']) ?>"
                                                       class="btn btn-outline-primary btn-sm mb-1"
                                                       title="View Applications">
                                                        <i class="fas fa-file-alt me-1"></i>
                                                        View Applications
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Information
                    </h6>
                    <p class="card-text">
                        This report shows all positions in the exercise that have received at least one application. 
                        Click "View Applications" to see the detailed application register for each position.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']], // Sort by position name
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting for Actions column
        ]
    });
});
</script>
<?= $this->endSection() ?>
