<?php
/**
 * View file for Application Register Report
 *
 * @var array $position Position details
 * @var array $applications List of applications
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Application Register - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Application Register Report</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Application Summary Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary mb-1"><?= $statistics['total_applications'] ?></h4>
                                <p class="mb-0 text-muted">Total Applications</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                <i class="fas fa-users fa-2x text-success mb-2"></i>
                                <h4 class="text-success mb-1"><?= $statistics['total_applicants'] ?></h4>
                                <p class="mb-0 text-muted">Total Applicants</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('reports/positions-with-applications/' . $exercise['id']) ?>"
                               class="text-decoration-none">
                                <div class="border rounded p-3 h-100 position-card-hover">
                                    <i class="fas fa-briefcase fa-2x text-info mb-2"></i>
                                    <h4 class="text-info mb-1"><?= $statistics['total_positions_with_applications'] ?></h4>
                                    <p class="mb-0 text-muted">Positions with Applications</p>
                                    <small class="text-info">
                                        <i class="fas fa-eye me-1"></i>Click to view details
                                    </small>
                                </div>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('reports/positions-without-applications/' . $exercise['id']) ?>"
                               class="text-decoration-none">
                                <div class="border rounded p-3 h-100 position-card-hover">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning mb-1"><?= $statistics['total_positions_without_applications'] ?></h4>
                                    <p class="mb-0 text-muted">Positions without Applications</p>
                                    <small class="text-warning">
                                        <i class="fas fa-eye me-1"></i>Click to view details
                                    </small>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Register -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Application Register (<?= count($applications) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applications)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">No applications have been received for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="applicationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Application No.</th>
                                        <th>Full Name</th>
                                        <th>Gender</th>
                                        <th>Date of Birth</th>
                                        <th>Place of Origin</th>
                                        <th>Contact Details</th>
                                        <th>Current Employment</th>
                                        <th>Status</th>
                                        <th>Received Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $index => $application): ?>
                                        <?php 
                                        $contact = json_decode($application['contact_details'], true);
                                        ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong><?= esc($application['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></strong>
                                                </div>
                                            </td>
                                            <td><?= esc($application['gender']) ?></td>
                                            <td><?= date('M d, Y', strtotime($application['date_of_birth'])) ?></td>
                                            <td><?= esc($application['place_of_origin']) ?></td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Phone:</strong> <?= esc($contact['phone'] ?? 'N/A') ?></div>
                                                    <div><strong>Email:</strong> <?= esc($contact['email'] ?? 'N/A') ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Employer:</strong> <?= esc($application['current_employer']) ?></div>
                                                    <div><strong>Position:</strong> <?= esc($application['current_position']) ?></div>
                                                    <div><strong>Salary:</strong> <?= esc($application['current_salary']) ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?= ucfirst($application['application_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?= date('M d, Y', strtotime($application['received_at'])) ?>
                                                    <br>
                                                    <?= date('H:i', strtotime($application['received_at'])) ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>


</div>

<script>
// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#applicationsTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[1, 'asc']],
            columnDefs: [
                { orderable: false, targets: [6, 7] }
            ]
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.position-card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.position-card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #007bff !important;
}
</style>
<?= $this->endSection() ?>
