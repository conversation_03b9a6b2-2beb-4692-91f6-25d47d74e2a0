<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionsModel extends Model
{
    protected $table         = 'positions';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'exercise_id',
        'org_id',
        'position_group_id',
        'position_reference',
        'designation',
        'classification',
        'award',
        'location',
        'annual_salary',
        'qualifications',
        'knowledge',
        'skills_competencies',
        'job_experiences',
        'jd_filepath',
        'jd_texts_extracted',
        'remarks',
        'status',
        'status_at',
        'status_by',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation rules
    protected $validationRules = [
        'org_id'             => 'required|numeric',
        'position_reference' => 'required|max_length[50]',
        'designation'       => 'required|max_length[255]',
        'classification'    => 'required|max_length[100]',
        'award'            => 'required|max_length[100]',
        'location'         => 'required|max_length[255]',
        'annual_salary'    => 'required|max_length[100]',
        'qualifications'   => 'permit_empty',
        'knowledge'        => 'permit_empty',
        'skills_competencies' => 'permit_empty',
        'job_experiences'  => 'permit_empty',
        'jd_filepath'      => 'permit_empty|max_length[255]',
        'status'          => 'required|max_length[20]',
        'created_by'      => 'required|numeric',
        'updated_by'      => 'required|numeric'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'position_reference' => [
            'required' => 'Position reference is required',
            'max_length' => 'Position reference cannot exceed 50 characters'
        ],
        'designation' => [
            'required' => 'Designation is required',
            'max_length' => 'Designation cannot exceed 255 characters'
        ]
    ];

    /**
     * Get published positions with organization and exercise information
     *
     * @param int $limit Number of positions to return (0 for all)
     * @return array
     */
    public function getPublishedPositions($limit = 0)
    {
        $currentDate = date('Y-m-d');

        $builder = $this->select('
            positions.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            positions_groups.group_name,
            positions_groups.exercise_id,
            exercises.exercise_name,
            exercises.advertisement_no,
            exercises.publish_date_from,
            exercises.publish_date_to
        ')
        ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
        ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
        ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
        ->where('(positions.status = 1 OR positions.status = "active")') // Handle both numeric and string status
        ->where('(exercises.id IS NULL OR exercises.status = "publish")')
        ->orderBy('positions.id', 'DESC');

        if ($limit > 0) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get random published positions with organization and exercise information
     *
     * @param int $limit Number of positions to return
     * @return array
     */
    public function getRandomPublishedPositions($limit = 10)
    {
        $currentDate = date('Y-m-d');

        return $this->select('
            positions.*,
            dakoii_org.org_name,
            positions_groups.group_name,
            positions_groups.exercise_id,
            exercises.exercise_name,
            exercises.advertisement_no,
            exercises.publish_date_to
        ')
        ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
        ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
        ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
        ->where('(positions.status = 1 OR positions.status = "active")') // Handle both numeric and string status
        ->where('(exercises.id IS NULL OR exercises.status = "publish")')
        ->orderBy('RAND()')
        ->limit($limit)
        ->findAll();
    }

    /**
     * Get all published positions with related data
     *
     * @return array
     */
    public function getAllPublishedPositions()
    {
        $today = date('Y-m-d');

        // Get positions that are active
        $builder = $this->select('
            positions.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            positions_groups.group_name,
            positions_groups.exercise_id,
            exercises.exercise_name,
            exercises.advertisement_no,
            exercises.status as exercise_status,
            exercises.publish_date_from,
            exercises.publish_date_to
        ')
        ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
        ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
        ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
        ->where('(positions.status = 1 OR positions.status = "active")') // Handle both numeric and string status
        ->where('(exercises.id IS NULL OR exercises.status = "publish")')
        ->orderBy('positions.id', 'DESC');

        // Log the number of positions for debugging
        log_message('debug', 'Getting all published positions');

        return $builder->findAll();
    }

    /**
     * Get positions by position group ID
     *
     * @param int $positionGroupId
     * @return array
     */
    public function getPositionsByGroupId($positionGroupId)
    {
        return $this->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.position_group_id', $positionGroupId)
            ->where('(positions.status = 1 OR positions.status = "active")')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();
    }

    /**
     * Get positions by position group ID with application counts
     *
     * @param int $positionGroupId
     * @return array
     */
    public function getPositionsByGroupIdWithApplicationCount($positionGroupId)
    {
        return $this->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                COUNT(appx_application_details.id) as application_count
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
            ->where('positions.position_group_id', $positionGroupId)
            ->where('(positions.status = 1 OR positions.status = "active")')
            ->groupBy('positions.id')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();
    }

    /**
     * Get all positions for a specific exercise with group and organization details
     * Used for reports
     *
     * @param int $exerciseId Exercise ID
     * @return array
     */
    public function getPositionsByExerciseId($exerciseId)
    {
        return $this->select('
            positions.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            positions_groups.group_name,
            positions_groups.exercise_id,
            exercises.exercise_name,
            exercises.advertisement_no
        ')
        ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
        ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
        ->where('positions_groups.exercise_id', $exerciseId)
        ->where('(positions.status = 1 OR positions.status = "active")') // Only active positions
        ->orderBy('positions_groups.group_name', 'ASC')
        ->orderBy('positions.designation', 'ASC')
        ->findAll();
    }
}