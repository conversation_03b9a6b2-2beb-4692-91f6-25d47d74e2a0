<?php
/**
 * View file for Positions Report
 *
 * @var array $exercise Exercise details
 * @var array $positions List of positions in the exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Positions Report - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Positions Report</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($positions) ?></h4>
                            <p class="mb-0">Total Positions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-briefcase fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($positions, function($p) { return !empty($p['jd_filepath']); })) ?></h4>
                            <p class="mb-0">With Job Description</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-pdf fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($positions, function($p) { return empty($p['jd_filepath']); })) ?></h4>
                            <p class="mb-0">Without Job Description</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count(array_unique(array_column($positions, 'group_name'))) ?></h4>
                            <p class="mb-0">Position Groups</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        All Positions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Positions Found</h5>
                            <p class="text-muted">No positions have been created for this exercise yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table id="positionsTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Position Reference</th>
                                        <th>Designation</th>
                                        <th>Position Group</th>
                                        <th>Classification</th>
                                        <th>Award</th>
                                        <th>Location</th>
                                        <th>Annual Salary</th>
                                        <th>JD Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $index => $position): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td><?= esc($position['position_reference']) ?></td>
                                            <td><?= esc($position['designation']) ?></td>
                                            <td><?= esc($position['group_name'] ?? 'No Group') ?></td>
                                            <td><?= esc($position['classification']) ?></td>
                                            <td><?= esc($position['award']) ?></td>
                                            <td><?= esc($position['location']) ?></td>
                                            <td><?= esc($position['annual_salary']) ?></td>
                                            <td>
                                                <?php if (!empty($position['jd_filepath'])): ?>
                                                    Yes
                                                <?php else: ?>
                                                    No
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

<!-- XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // Add export button before initializing DataTable
    $('#positionsTable').before(
        '<div class="mb-3">' +
        '<button id="exportExcel" class="btn btn-success">' +
        '<i class="fas fa-file-excel me-1"></i> Export to Excel' +
        '</button>' +
        '</div>'
    );

    // Initialize DataTable (simple configuration without buttons)
    var table = $('#positionsTable').DataTable({
        paging: false,
        searching: true,
        ordering: true,
        info: true,
        language: {
            search: "Search positions:",
            info: "Showing _TOTAL_ positions",
            infoEmpty: "No positions available",
            infoFiltered: "(filtered from _MAX_ total positions)"
        },
        columnDefs: [
            { orderable: false, targets: 0 } // Disable sorting on # column
        ]
    });

    // Excel export functionality
    $('#exportExcel').on('click', function() {
        // Get the table data
        var wb = XLSX.utils.table_to_book(document.getElementById('positionsTable'), {
            sheet: "Positions Report"
        });

        // Generate filename
        var filename = 'Positions_Report_<?= str_replace(' ', '_', $exercise['exercise_name']) ?>_<?= date('Y-m-d') ?>.xlsx';

        // Download the file
        XLSX.writeFile(wb, filename);
    });
});
</script>

<?= $this->endSection() ?>
