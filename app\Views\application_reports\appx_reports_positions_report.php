<?php
/**
 * View file for Positions Report
 *
 * @var array $exercise Exercise details
 * @var array $positions List of positions in the exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Positions Report - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Positions Report</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($positions) ?></h4>
                            <p class="mb-0">Total Positions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-briefcase fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($positions, function($p) { return !empty($p['jd_filepath']); })) ?></h4>
                            <p class="mb-0">With Job Description</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-pdf fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($positions, function($p) { return empty($p['jd_filepath']); })) ?></h4>
                            <p class="mb-0">Without Job Description</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count(array_unique(array_column($positions, 'group_name'))) ?></h4>
                            <p class="mb-0">Position Groups</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        All Positions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Positions Found</h5>
                            <p class="text-muted">No positions have been created for this exercise yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Position Reference</th>
                                        <th>Designation</th>
                                        <th>Position Group</th>
                                        <th>Classification</th>
                                        <th>Award</th>
                                        <th>Location</th>
                                        <th>Annual Salary</th>
                                        <th>JD Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $index => $position): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <span class="badge bg-primary"><?= esc($position['position_reference']) ?></span>
                                            </td>
                                            <td>
                                                <strong><?= esc($position['designation']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?= esc($position['group_name'] ?? 'No Group') ?></span>
                                            </td>
                                            <td><?= esc($position['classification']) ?></td>
                                            <td><?= esc($position['award']) ?></td>
                                            <td><?= esc($position['location']) ?></td>
                                            <td><?= esc($position['annual_salary']) ?></td>
                                            <td>
                                                <?php if (!empty($position['jd_filepath'])): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>
                                                        Has JD
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times me-1"></i>
                                                        No JD
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <?php if (!empty($positions)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-download me-2"></i>
                            Export Options
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <button class="btn btn-success w-100" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Export to Excel
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button class="btn btn-danger w-100" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    Export to PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function exportToExcel() {
    // Simple table to Excel export
    const table = document.querySelector('table');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Positions Report"});
    XLSX.writeFile(wb, 'positions_report_<?= $exercise['exercise_name'] ?>_<?= date('Y-m-d') ?>.xlsx');
}

function exportToPDF() {
    window.print();
}

// Add print styles
const style = document.createElement('style');
style.textContent = `
    @media print {
        .btn, .card-header, nav, .breadcrumb { display: none !important; }
        .card { border: none !important; box-shadow: none !important; }
        .table { font-size: 12px; }
    }
`;
document.head.appendChild(style);
</script>

<!-- Include XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<?= $this->endSection() ?>
